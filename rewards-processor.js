require('dotenv').config();
const Database = require('./src/core/database');
const SolanaTokenChecker = require('./src/core/solana-token-checker');
const RewardCalculator = require('./src/core/reward-calculator');

class RewardsProcessor {
    constructor() {
        this.database = null;
        this.tokenChecker = null;
        this.rewardCalculator = null;
    }

    async init() {
        console.log('🚀 Initializing Rewards Processor...');
        this.database = new Database();
        await this.database.init();
        await this.createRewardsTables();
        await this.migrateDatabase();

        // Initialize Solana token checker
        this.tokenChecker = new SolanaTokenChecker();
        await this.tokenChecker.init();

        // Initialize reward calculator
        this.rewardCalculator = new RewardCalculator();
        await this.rewardCalculator.init();

        console.log('✅ Rewards Processor initialized');
    }

    async createRewardsTables() {
        // Create rewards tracking tables
        const tables = [
            // Reward distributions table - tracks when rewards were sent
            `CREATE TABLE IF NOT EXISTS reward_distributions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                distribution_timestamp DATETIME NOT NULL,
                total_players INTEGER DEFAULT 0,
                total_viewers INTEGER DEFAULT 0,
                total_amount_distributed REAL DEFAULT 0,
                player_amount REAL DEFAULT 0,
                viewer_amount REAL DEFAULT 0,
                creator_vault_balance_sol REAL DEFAULT 0,
                total_for_rewards_sol REAL DEFAULT 0,
                from_timestamp DATETIME NOT NULL,
                to_timestamp DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Individual reward records - tracks rewards per user
            `CREATE TABLE IF NOT EXISTS user_rewards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                distribution_id INTEGER,
                user_id INTEGER,
                username TEXT NOT NULL,
                user_address TEXT,
                reward_type TEXT NOT NULL CHECK (reward_type IN ('player', 'viewer')),
                amount REAL NOT NULL,
                percentage_share REAL DEFAULT 0,
                sol_amount REAL DEFAULT 0,
                score INTEGER DEFAULT 0,
                token_balance REAL DEFAULT 0,
                balance_amount REAL DEFAULT 0,
                balance_amount_ui REAL DEFAULT 0,
                is_eligible BOOLEAN DEFAULT FALSE,
                has_been_rewarded BOOLEAN DEFAULT FALSE,
                balance_check_error TEXT,
                rank INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (distribution_id) REFERENCES reward_distributions (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // User eligibility tracking - tracks token balance eligibility per processing period
            `CREATE TABLE IF NOT EXISTS user_eligibility (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                user_address TEXT,
                token_account_address TEXT,
                reward_type TEXT NOT NULL CHECK (reward_type IN ('player', 'viewer')),
                processing_timestamp DATETIME NOT NULL,
                token_balance REAL DEFAULT 0,
                balance_amount REAL DEFAULT 0,
                balance_amount_ui REAL DEFAULT 0,
                minimum_required_balance REAL NOT NULL,
                is_eligible BOOLEAN DEFAULT FALSE,
                has_been_rewarded BOOLEAN DEFAULT FALSE,
                balance_check_error TEXT,
                score INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(user_id, processing_timestamp, reward_type)
            )`,

            // Last processed timestamp tracking
            `CREATE TABLE IF NOT EXISTS processing_state (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                last_processed_timestamp DATETIME NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.database.runQuery(table);
        }

        // Create indexes for optimization
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_reward_distributions_timestamp ON reward_distributions (distribution_timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_user_rewards_distribution_id ON user_rewards (distribution_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_rewards_user_id ON user_rewards (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_rewards_type ON user_rewards (reward_type)'
        ];

        for (const index of indexes) {
            await this.database.runQuery(index);
        }

        console.log('✅ Rewards tables and indexes created successfully');
    }

    async migrateDatabase() {
        try {
            console.log('🔄 Checking for database migrations...');

            // Check if balance_amount columns exist in user_eligibility table
            const tableInfo = await this.database.allQuery(`PRAGMA table_info(user_eligibility)`);
            const columnNames = tableInfo.map(col => col.name);

            if (!columnNames.includes('balance_amount')) {
                console.log('📝 Adding balance_amount column to user_eligibility table...');
                await this.database.runQuery(`ALTER TABLE user_eligibility ADD COLUMN balance_amount REAL DEFAULT 0`);
            }

            if (!columnNames.includes('balance_amount_ui')) {
                console.log('📝 Adding balance_amount_ui column to user_eligibility table...');
                await this.database.runQuery(`ALTER TABLE user_eligibility ADD COLUMN balance_amount_ui REAL DEFAULT 0`);
            }

            // Check if balance_amount columns exist in user_rewards table
            const rewardsTableInfo = await this.database.allQuery(`PRAGMA table_info(user_rewards)`);
            const rewardsColumnNames = rewardsTableInfo.map(col => col.name);

            if (!rewardsColumnNames.includes('balance_amount')) {
                console.log('📝 Adding balance_amount column to user_rewards table...');
                await this.database.runQuery(`ALTER TABLE user_rewards ADD COLUMN balance_amount REAL DEFAULT 0`);
            }

            if (!rewardsColumnNames.includes('balance_amount_ui')) {
                console.log('📝 Adding balance_amount_ui column to user_rewards table...');
                await this.database.runQuery(`ALTER TABLE user_rewards ADD COLUMN balance_amount_ui REAL DEFAULT 0`);
            }

            if (!rewardsColumnNames.includes('percentage_share')) {
                console.log('📝 Adding percentage_share column to user_rewards table...');
                await this.database.runQuery(`ALTER TABLE user_rewards ADD COLUMN percentage_share REAL DEFAULT 0`);
            }

            if (!rewardsColumnNames.includes('sol_amount')) {
                console.log('📝 Adding sol_amount column to user_rewards table...');
                await this.database.runQuery(`ALTER TABLE user_rewards ADD COLUMN sol_amount REAL DEFAULT 0`);
            }

            // Check if new columns exist in reward_distributions table
            const distributionsTableInfo = await this.database.allQuery(`PRAGMA table_info(reward_distributions)`);
            const distributionsColumnNames = distributionsTableInfo.map(col => col.name);

            if (!distributionsColumnNames.includes('creator_vault_balance_sol')) {
                console.log('📝 Adding creator_vault_balance_sol column to reward_distributions table...');
                await this.database.runQuery(`ALTER TABLE reward_distributions ADD COLUMN creator_vault_balance_sol REAL DEFAULT 0`);
            }

            if (!distributionsColumnNames.includes('total_for_rewards_sol')) {
                console.log('📝 Adding total_for_rewards_sol column to reward_distributions table...');
                await this.database.runQuery(`ALTER TABLE reward_distributions ADD COLUMN total_for_rewards_sol REAL DEFAULT 0`);
            }

            console.log('✅ Database migration completed');

        } catch (error) {
            console.error('❌ Database migration failed:', error);
            throw error;
        }
    }

    async getLastProcessedTimestamp() {
        try {
            const result = await this.database.getQuery(
                'SELECT last_processed_timestamp FROM processing_state WHERE id = 1'
            );

            if (result) {
                return new Date(result.last_processed_timestamp);
            } else {
                // If no record exists, start from 24 hours ago
                const defaultTimestamp = new Date(Date.now() - 24 * 60 * 60 * 1000);
                await this.database.runQuery(
                    'INSERT INTO processing_state (id, last_processed_timestamp) VALUES (1, ?)',
                    [defaultTimestamp.toISOString()]
                );
                return defaultTimestamp;
            }
        } catch (error) {
            console.error('Error getting last processed timestamp:', error);
            throw error;
        }
    }

    async updateLastProcessedTimestamp(timestamp) {
        try {
            await this.database.runQuery(
                `INSERT OR REPLACE INTO processing_state (id, last_processed_timestamp, updated_at) 
                 VALUES (1, ?, CURRENT_TIMESTAMP)`,
                [timestamp.toISOString()]
            );
        } catch (error) {
            console.error('Error updating last processed timestamp:', error);
            throw error;
        }
    }

    async getPlayersWithMoves(fromTimestamp) {
        try {
            const sql = `
                SELECT 
                    u.id,
                    u.username,
                    u.user_address,
                    COUNT(cm.id) as total_moves,
                    COUNT(CASE WHEN cm.is_valid_move = 1 THEN 1 END) as valid_moves,
                    MIN(cm.timestamp) as first_move,
                    MAX(cm.timestamp) as last_move
                FROM users u
                INNER JOIN chat_messages cm ON u.id = cm.user_id
                WHERE cm.timestamp > ? AND cm.is_valid_move = 1
                GROUP BY u.id, u.username, u.user_address
                ORDER BY valid_moves DESC, total_moves DESC
            `;

            const players = await this.database.allQuery(sql, [fromTimestamp.toISOString()]);
            console.log(`🎮 Found ${players.length} players with valid moves since ${fromTimestamp.toLocaleString()}`);

            return players;
        } catch (error) {
            console.error('Error getting players with moves:', error);
            throw error;
        }
    }

    async getViewers(fromTimestamp) {
        try {
            const sql = `
                SELECT 
                    u.id,
                    u.username,
                    u.user_address,
                    COUNT(DISTINCT vs.scrape_session_id) as viewing_sessions,
                    MIN(vs.session_timestamp) as first_seen,
                    MAX(vs.session_timestamp) as last_seen,
                    vs.user_role
                FROM users u
                INNER JOIN viewer_sessions vs ON u.id = vs.user_id
                WHERE vs.session_timestamp > ?
                AND user_role = 'viewer'
                GROUP BY u.id, u.username, u.user_address, vs.user_role
                ORDER BY viewing_sessions DESC
            `;

            const allViewers = await this.database.allQuery(sql, [fromTimestamp.toISOString()]);
            console.log(`👥 Found ${allViewers.length} total viewers since ${fromTimestamp.toLocaleString()}`);

            return allViewers;
        } catch (error) {
            console.error('Error getting viewers:', error);
            throw error;
        }
    }

    async removePlayersFromViewers(players, viewers) {
        // Create a Set of player usernames for fast lookup
        const playerUsernames = new Set(players.map(p => p.username));

        // Filter out viewers who are also players
        const viewersOnly = viewers.filter(viewer => !playerUsernames.has(viewer.username));

        console.log(`🔄 Removed ${viewers.length - viewersOnly.length} viewers who are also players`);
        console.log(`👥 Final viewer count: ${viewersOnly.length}`);

        return viewersOnly;
    }

    async checkTokenBalancesAndStoreEligibility(users, rewardType, processingTimestamp) {
        try {
            console.log(`\n💰 Checking token balances for ${users.length} ${rewardType}s...`);

            if (users.length === 0) {
                return [];
            }

            // Check token balances for all users
            const usersWithBalances = await this.tokenChecker.checkMultipleBalances(users);

            // Store eligibility data in database
            await this.storeEligibilityData(usersWithBalances, rewardType, processingTimestamp);

            // Filter to only eligible users
            const eligibleUsers = usersWithBalances.filter(user => user.isEligible);
            const ineligibleUsers = usersWithBalances.filter(user => !user.isEligible);

            console.log(`✅ Token balance check for ${rewardType}s complete:`);
            console.log(`   Total checked: ${usersWithBalances.length}`);
            console.log(`   Eligible: ${eligibleUsers.length}`);
            console.log(`   Ineligible: ${ineligibleUsers.length}`);

            if (ineligibleUsers.length > 0) {
                console.log(`\n❌ Ineligible ${rewardType}s (insufficient token balance):`);
                ineligibleUsers.forEach((user, index) => {
                    const balanceStr = (user.balanceAmountUI || 0).toLocaleString();
                    const requiredStr = process.env.MINIMUM_TOKEN_BALANCE_UI || '50000';
                    console.log(`   ${index + 1}. ${user.username} - Balance: ${balanceStr} (Required: ${requiredStr})`);
                });
            }

            return eligibleUsers;

        } catch (error) {
            console.error(`❌ Error checking token balances for ${rewardType}s:`, error);
            throw error;
        }
    }

    async storeEligibilityData(users, rewardType, processingTimestamp) {
        try {
            const minimumBalance = parseInt(process.env.MINIMUM_TOKEN_BALANCE_UI) || 50000;

            for (const user of users) {
                await this.database.runQuery(`
                    INSERT OR REPLACE INTO user_eligibility (
                        user_id, username, user_address, token_account_address, reward_type, processing_timestamp,
                        token_balance, balance_amount, balance_amount_ui, minimum_required_balance,
                        is_eligible, has_been_rewarded, balance_check_error, score
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    user.id,
                    user.username,
                    user.user_address,
                    user.tokenAccountAddress || null,
                    rewardType,
                    processingTimestamp.toISOString(),
                    user.tokenBalance || 0, // Keep for backward compatibility
                    user.balanceAmount || 0,
                    user.balanceAmountUI || 0,
                    minimumBalance,
                    user.isEligible ? 1 : 0,
                    0, // has_been_rewarded - default to false
                    user.balanceCheckError || null,
                    user.valid_moves || user.viewing_sessions || 0
                ]);
            }

            console.log(`📝 Stored eligibility data for ${users.length} ${rewardType}s`);

        } catch (error) {
            console.error(`❌ Error storing eligibility data for ${rewardType}s:`, error);
            throw error;
        }
    }

    async processRewards() {
        try {
            console.log('\n🎯 Starting Rewards Processing...');
            console.log('='.repeat(50));

            // Get the last processed timestamp
            const fromTimestamp = await this.getLastProcessedTimestamp();
            const toTimestamp = new Date();

            console.log(`📅 Processing period: ${fromTimestamp.toLocaleString()} to ${toTimestamp.toLocaleString()}`);

            // 1. Get all players and score them by their amount of moves
            const players = await this.getPlayersWithMoves(fromTimestamp);

            // 2. Get all viewers and remove usernames that are also players
            const allViewers = await this.getViewers(fromTimestamp);
            const viewers = await this.removePlayersFromViewers(players, allViewers);

            // 3. Check token balances for eligibility
            const eligiblePlayers = await this.checkTokenBalancesAndStoreEligibility(players, 'player', toTimestamp);
            const eligibleViewers = await this.checkTokenBalancesAndStoreEligibility(viewers, 'viewer', toTimestamp);

            // 4. Calculate SOL rewards from creator vault
            const rewardDistribution = await this.rewardCalculator.calculateAndPrintRewards(eligiblePlayers, eligibleViewers);

            // 5. Store reward distribution in database
            await this.storeRewardDistribution(rewardDistribution, eligiblePlayers, eligibleViewers, fromTimestamp, toTimestamp);

            // 6. Print integrated results
            this.printIntegratedResults(eligiblePlayers, eligibleViewers, rewardDistribution);

            // Update the last processed timestamp
            await this.updateLastProcessedTimestamp(toTimestamp);

            return {
                players: eligiblePlayers,
                viewers: eligibleViewers,
                allPlayers: players,
                allViewers: viewers,
                rewardDistribution,
                fromTimestamp,
                toTimestamp
            };

        } catch (error) {
            console.error('💥 Error processing rewards:', error);
            throw error;
        }
    }

    async storeRewardDistribution(rewardDistribution, eligiblePlayers, eligibleViewers, fromTimestamp, toTimestamp) {
        try {
            console.log('\n💾 Storing reward distribution in database...');

            // Insert distribution record
            const distributionResult = await this.database.runQuery(`
                INSERT INTO reward_distributions (
                    distribution_timestamp, total_players, total_viewers,
                    total_amount_distributed, player_amount, viewer_amount,
                    creator_vault_balance_sol, total_for_rewards_sol,
                    from_timestamp, to_timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                new Date().toISOString(),
                eligiblePlayers.length,
                eligibleViewers.length,
                rewardDistribution.totalForRewards,
                rewardDistribution.totalForPlayers,
                rewardDistribution.totalForViewers,
                rewardDistribution.totalSOL,
                rewardDistribution.totalForRewards,
                fromTimestamp.toISOString(),
                toTimestamp.toISOString()
            ]);

            const distributionId = distributionResult.lastID;

            // Store individual player rewards
            for (const player of rewardDistribution.playerRewards) {
                await this.database.runQuery(`
                    INSERT INTO user_rewards (
                        distribution_id, username, user_address, reward_type,
                        amount, percentage_share, sol_amount, score, rank
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    distributionId,
                    player.username,
                    player.user_address,
                    'player',
                    player.rewardSOL, // This will be the token amount later
                    player.percentage,
                    player.rewardSOL,
                    player.moves,
                    player.rank
                ]);
            }

            // Store individual viewer rewards
            for (const viewer of rewardDistribution.viewerRewards) {
                await this.database.runQuery(`
                    INSERT INTO user_rewards (
                        distribution_id, username, user_address, reward_type,
                        amount, percentage_share, sol_amount, score, rank
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    distributionId,
                    viewer.username,
                    viewer.user_address,
                    'viewer',
                    viewer.rewardSOL, // This will be the token amount later
                    viewer.percentage,
                    viewer.rewardSOL,
                    viewer.viewing_sessions || 0,
                    viewer.rank
                ]);
            }

            console.log(`✅ Stored reward distribution with ID: ${distributionId}`);
            return distributionId;

        } catch (error) {
            console.error('❌ Error storing reward distribution:', error);
            throw error;
        }
    }

    printResults(players, viewers) {
        const minimumBalance = parseInt(process.env.MINIMUM_TOKEN_BALANCE_UI) || 50000;

        console.log('\n📊 REWARDS PROCESSING RESULTS');
        console.log('='.repeat(50));

        console.log('\n🎮 ELIGIBLE PLAYERS (Token Balance ≥ ' + minimumBalance.toLocaleString() + '):');
        console.log('-'.repeat(50));
        if (players.length === 0) {
            console.log('  No eligible players found with valid moves in this period.');
        } else {
            players.forEach((player, index) => {
                console.log(`  ${index + 1}. ${player.username} ✅`);
                console.log(`     Valid Moves: ${player.valid_moves}`);
                console.log(`     Total Messages: ${player.total_moves}`);
                console.log(`     Address: ${player.user_address || 'N/A'}`);
                console.log(`     Token Balance: ${(player.balanceAmountUI || 0).toLocaleString()}`);
                console.log(`     Period: ${new Date(player.first_move).toLocaleString()} - ${new Date(player.last_move).toLocaleString()}`);
                console.log('');
            });
        }

        console.log('\n👥 ELIGIBLE VIEWERS (Token Balance ≥ ' + minimumBalance.toLocaleString() + '):');
        console.log('-'.repeat(50));
        if (viewers.length === 0) {
            console.log('  No eligible viewers found in this period.');
        } else {
            viewers.forEach((viewer, index) => {
                console.log(`  ${index + 1}. ${viewer.username} (${viewer.user_role}) ✅`);
                console.log(`     Viewing Sessions: ${viewer.viewing_sessions}`);
                console.log(`     Address: ${viewer.user_address || 'N/A'}`);
                console.log(`     Token Balance: ${(viewer.balanceAmountUI || 0).toLocaleString()}`);
                console.log(`     Period: ${new Date(viewer.first_seen).toLocaleString()} - ${new Date(viewer.last_seen).toLocaleString()}`);
                console.log('');
            });
        }

        console.log(`\n📈 ELIGIBILITY SUMMARY:`);
        console.log(`  Eligible Players: ${players.length}`);
        console.log(`  Eligible Viewers: ${viewers.length}`);
        console.log(`  Total Eligible for Rewards: ${players.length + viewers.length}`);
        console.log(`  Minimum Token Balance Required: ${minimumBalance.toLocaleString()}`);
    }

    printIntegratedResults(eligiblePlayers, eligibleViewers, rewardDistribution) {
        console.log('\n' + '='.repeat(80));
        console.log('🎯 INTEGRATED REWARDS SYSTEM - FINAL SUMMARY');
        console.log('='.repeat(80));

        console.log(`\n💰 CREATOR VAULT & DISTRIBUTION:`);
        console.log(`   Creator Vault Balance: ${rewardDistribution.totalSOL.toFixed(9)} SOL`);
        console.log(`   Total for Rewards (55%): ${rewardDistribution.totalForRewards.toFixed(9)} SOL`);
        console.log(`   Reserved for Bounties (30%): ${(rewardDistribution.totalSOL * 0.3).toFixed(9)} SOL`);
        console.log(`   Reserved for Reserves (15%): ${(rewardDistribution.totalSOL * 0.15).toFixed(9)} SOL`);

        console.log(`\n🎮 PLAYER REWARDS (30% = ${rewardDistribution.totalForPlayers.toFixed(9)} SOL):`);
        if (rewardDistribution.playerRewards.length > 0) {
            console.log(`   Top 5 Players:`);
            rewardDistribution.playerRewards.slice(0, 5).forEach(player => {
                console.log(`   ${player.rank}. ${player.username.padEnd(20)} | ${player.moves.toString().padStart(3)} moves | ${player.percentage.toFixed(2)}% | ${player.rewardSOL.toFixed(9)} SOL`);
            });
            if (rewardDistribution.playerRewards.length > 5) {
                console.log(`   ... and ${rewardDistribution.playerRewards.length - 5} more players`);
            }
        } else {
            console.log(`   No eligible players found`);
        }

        console.log(`\n👥 VIEWER REWARDS (25% = ${rewardDistribution.totalForViewers.toFixed(9)} SOL):`);
        if (rewardDistribution.viewerRewards.length > 0) {
            console.log(`   ${rewardDistribution.viewerRewards.length} viewers each get: ${rewardDistribution.viewerRewards[0].rewardSOL.toFixed(9)} SOL (${rewardDistribution.viewerRewards[0].percentage.toFixed(2)}%)`);
            console.log(`   Sample viewers:`);
            rewardDistribution.viewerRewards.slice(0, 3).forEach(viewer => {
                console.log(`   - ${viewer.username.padEnd(20)} | ${viewer.rewardSOL.toFixed(9)} SOL`);
            });
            if (rewardDistribution.viewerRewards.length > 3) {
                console.log(`   ... and ${rewardDistribution.viewerRewards.length - 3} more viewers`);
            }
        } else {
            console.log(`   No eligible viewers found`);
        }

        console.log(`\n📊 SUMMARY:`);
        console.log(`   Total Eligible Users: ${eligiblePlayers.length + eligibleViewers.length}`);
        console.log(`   Players: ${eligiblePlayers.length} | Viewers: ${eligibleViewers.length}`);
        console.log(`   Total SOL to Distribute: ${rewardDistribution.totalForRewards.toFixed(9)} SOL`);
        console.log(`   Stored in Database: ✅`);

        console.log('\n📋 NEXT STEPS:');
        console.log('   1. Review the reward distribution above');
        console.log('   2. Implement token conversion (SOL → TOKENS)');
        console.log('   3. Execute actual token transfers');
        console.log('   4. Update reward status in database');

        console.log('\n' + '='.repeat(80));
    }

    async close() {
        if (this.tokenChecker) {
            await this.tokenChecker.close();
        }
        if (this.database) {
            await this.database.close();
            console.log('✅ Rewards Processor closed');
        }
    }
}

// CLI interface
async function main() {
    const processor = new RewardsProcessor();

    try {
        await processor.init();
        await processor.processRewards();
    } catch (error) {
        console.error('💥 Fatal error:', error);
    } finally {
        await processor.close();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = RewardsProcessor;
