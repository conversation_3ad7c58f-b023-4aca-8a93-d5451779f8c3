require('dotenv').config();
const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { PumpAmmSdk } = require('@pump-fun/pump-swap-sdk');
const bs58 = require('bs58');
const BN = require('bn.js');

class RewardCalculator {
    constructor() {
        this.connection = null;
        this.pumpSdk = null;
        this.rpcUrl = process.env.RPC_URL || 'https://api.mainnet-beta.solana.com';
        this.coinAddress = process.env.COIN;
        this.Pk = process.env.PK; // Use PK instead of TEST_PK since TEST_PK is invalid

        // Reward configuration from environment
        this.maxHunterRewardUSD = parseFloat(process.env.MAX_HUNTER_REWARD_VALUE_USD) || 100;
        this.maxViewerRewardUSD = parseFloat(process.env.MAX_VIEWER_REWARD_VALUE_USD) || 50;

        // Distribution percentages
        this.reservesPct = parseFloat(process.env.RESERVES_PCT) || 0.15;
        this.bountiesPct = parseFloat(process.env.BOUNTIES_PCT) || 0.3;
        this.viewersPct = parseFloat(process.env.VIEWERS_PCT) || 0.25;
        this.playerPct = parseFloat(process.env.PLAYER_PCT) || 0.3;

        // Calculate total percentage for players and viewers (should be 60%)
        this.totalPlayerViewerPct = this.viewersPct + this.playerPct;

        console.log('🎯 Reward Calculator Configuration:');
        console.log(`   Max Hunter Reward: $${this.maxHunterRewardUSD}`);
        console.log(`   Max Viewer Reward: $${this.maxViewerRewardUSD}`);
        console.log(`   Reserves: ${(this.reservesPct * 100).toFixed(1)}%`);
        console.log(`   Bounties: ${(this.bountiesPct * 100).toFixed(1)}%`);
        console.log(`   Viewers: ${(this.viewersPct * 100).toFixed(1)}%`);
        console.log(`   Players: ${(this.playerPct * 100).toFixed(1)}%`);
        console.log(`   Total for Players+Viewers: ${(this.totalPlayerViewerPct * 100).toFixed(1)}%`);
    }

    async init() {
        try {
            console.log('\n🔗 Initializing Reward Calculator...');

            // Validate required environment variables
            if (!this.coinAddress) {
                throw new Error('COIN environment variable not set');
            }
            if (!this.Pk) {
                throw new Error('TEST_PK or PK environment variable not set');
            }

            // Initialize connection
            this.connection = new Connection(this.rpcUrl, 'confirmed');

            // Initialize Pump SDK
            this.pumpSdk = new PumpAmmSdk(this.connection);

            console.log(`✅ Reward Calculator initialized`);
            console.log(`   RPC URL: ${this.rpcUrl}`);
            console.log(`   Coin Address: ${this.coinAddress}`);

            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Reward Calculator:', error);
            throw error;
        }
    }

    async getCreatorVaultBalance() {
        try {
            console.log('\n💰 Checking creator vault balance...');

            // Validate the public key first
            let creatorPublicKey;
            try {
                const privateKeyBuffer = bs58.default.decode(this.Pk);
                const keypair = Keypair.fromSecretKey(privateKeyBuffer);
                creatorPublicKey = keypair.publicKey;
            } catch (keyError) {
                throw new Error(`Invalid creator public key: ${this.testPk}. Error: ${keyError.message}`);
            }

            // Get creator vault balance using pump SDK
            const vaultBalance = await this.pumpSdk.getCoinCreatorVaultBalance(creatorPublicKey);

            // Convert from lamports to SOL (1 SOL = 1,000,000,000 lamports)
            const vaultBalanceSOL = vaultBalance.toNumber() / 1_000_000_000;

            console.log(`✅ Creator vault balance: ${vaultBalanceSOL.toFixed(9)} SOL`);
            console.log(`   Raw lamports: ${vaultBalance.toString()}`);

            return {
                lamports: vaultBalance,
                sol: vaultBalanceSOL
            };
        } catch (error) {
            console.error('❌ Error checking creator vault balance:', error);
            throw error;
        }
    }

    calculateRewardDistribution(totalSOL, players, viewers) {
        console.log('\n🧮 Calculating reward distribution...');
        console.log(`📊 Input data:`);
        console.log(`   Total SOL available: ${totalSOL.toFixed(9)} SOL`);
        console.log(`   Number of players: ${players.length}`);
        console.log(`   Number of viewers: ${viewers.length}`);

        // Check if we need to promote viewers to players (when <50 players)
        const maxPlayers = parseInt(process.env.MAX_PLAYERS_REWARDED) || 50;
        let finalPlayers = [...players];
        let finalViewers = [...viewers];
        let promotedViewers = [];

        if (players.length < maxPlayers && viewers.length > 0) {
            const availableSlots = maxPlayers - players.length;
            const viewersToPromote = Math.min(availableSlots, viewers.length);

            console.log(`\n🔄 Promoting viewers to player rewards (${players.length} < ${maxPlayers} players):`);
            console.log(`   Available slots: ${availableSlots}`);
            console.log(`   Viewers to promote: ${viewersToPromote}`);

            // Sort viewers by viewing_sessions (watchtime) descending
            const sortedViewers = viewers.sort((a, b) => (b.viewing_sessions || 0) - (a.viewing_sessions || 0));

            // Promote top viewers to players
            promotedViewers = sortedViewers.slice(0, viewersToPromote).map(viewer => ({
                ...viewer,
                valid_moves: 0, // Promoted viewers have 0 moves but get ranked by watchtime
                promoted_from_viewer: true,
                original_viewing_sessions: viewer.viewing_sessions || 0
            }));

            finalPlayers = [...players, ...promotedViewers];
            finalViewers = sortedViewers.slice(viewersToPromote); // Remaining viewers

            console.log(`   Promoted ${promotedViewers.length} viewers to player rewards`);
            console.log(`   Remaining viewers: ${finalViewers.length}`);
        }

        // Calculate total available for players and viewers (60% of total)
        const totalForRewards = totalSOL * this.totalPlayerViewerPct;

        // Adjust distribution based on promotion
        let totalForPlayers, totalForViewers;

        if (promotedViewers.length > 0) {
            // When viewers are promoted, combine both pools for player rewards
            totalForPlayers = totalSOL * (this.playerPct + this.viewersPct);
            totalForViewers = 0; // No separate viewer rewards when promoted
            console.log(`\n💎 Adjusted distribution (viewers promoted):`);
            console.log(`   Total for rewards (${(this.totalPlayerViewerPct * 100).toFixed(1)}%): ${totalForRewards.toFixed(9)} SOL`);
            console.log(`   Total for players (${((this.playerPct + this.viewersPct) * 100).toFixed(1)}%): ${totalForPlayers.toFixed(9)} SOL`);
            console.log(`   Total for viewers: 0.000000000 SOL (promoted to players)`);
        } else {
            // Normal distribution
            totalForPlayers = totalSOL * this.playerPct;
            totalForViewers = totalSOL * this.viewersPct;
            console.log(`\n💎 Distribution breakdown:`);
            console.log(`   Total for rewards (${(this.totalPlayerViewerPct * 100).toFixed(1)}%): ${totalForRewards.toFixed(9)} SOL`);
            console.log(`   Total for players (${(this.playerPct * 100).toFixed(1)}%): ${totalForPlayers.toFixed(9)} SOL`);
            console.log(`   Total for viewers (${(this.viewersPct * 100).toFixed(1)}%): ${totalForViewers.toFixed(9)} SOL`);
        }

        // Calculate individual rewards
        const playerRewards = this.calculatePlayerRewards(totalForPlayers, finalPlayers, promotedViewers.length > 0);
        const viewerRewards = finalViewers.length > 0 ? this.calculateViewerRewards(totalForViewers, finalViewers) : [];

        return {
            totalSOL,
            totalForRewards,
            totalForPlayers,
            totalForViewers,
            playerRewards,
            viewerRewards,
            promotedViewers: promotedViewers.length,
            originalPlayers: players.length,
            originalViewers: viewers.length
        };
    }

    calculatePlayerRewards(totalForPlayers, players, hasPromotedViewers = false) {
        console.log(`\n🎮 Calculating player rewards with exponential decay...`);

        if (players.length === 0) {
            console.log(`   No players to reward`);
            return [];
        }

        // Sort players first: real players by moves, then promoted viewers by watchtime
        const realPlayers = players.filter(p => !p.promoted_from_viewer);
        const promotedViewers = players.filter(p => p.promoted_from_viewer);

        // Sort real players by moves (descending)
        realPlayers.sort((a, b) => (b.valid_moves || 0) - (a.valid_moves || 0));

        // Sort promoted viewers by viewing sessions (descending)
        promotedViewers.sort((a, b) => (b.original_viewing_sessions || 0) - (a.original_viewing_sessions || 0));

        // Combine: real players first, then promoted viewers
        const sortedPlayers = [...realPlayers, ...promotedViewers];

        console.log(`   Total players: ${sortedPlayers.length} (${realPlayers.length} real + ${promotedViewers.length} promoted)`);

        // Calculate exponential decay rewards
        // The decay factor determines how much each rank gets compared to the previous
        const decayFactor = 0.8; // Each rank gets 80% of the previous rank's reward

        // Calculate the sum of the geometric series to normalize
        let totalWeight = 0;
        for (let i = 0; i < sortedPlayers.length; i++) {
            totalWeight += Math.pow(decayFactor, i);
        }

        console.log(`   Using exponential decay (factor: ${decayFactor})`);
        console.log(`   Total weight for normalization: ${totalWeight.toFixed(6)}`);

        // Calculate individual rewards
        const playerRewards = sortedPlayers.map((player, index) => {
            const rank = index + 1;
            const weight = Math.pow(decayFactor, index);
            const rewardSOL = (weight / totalWeight) * totalForPlayers;
            const percentage = (weight / totalWeight) * 100;

            return {
                rank: rank,
                username: player.username,
                user_address: player.user_address,
                moves: player.valid_moves || 0,
                viewing_sessions: player.original_viewing_sessions || player.viewing_sessions || 0,
                promoted_from_viewer: player.promoted_from_viewer || false,
                rewardSOL: rewardSOL,
                percentage: percentage,
                weight: weight
            };
        });

        // Log the top rewards for verification
        console.log(`   Top 5 reward percentages:`);
        playerRewards.slice(0, 5).forEach(player => {
            const type = player.promoted_from_viewer ? '(promoted viewer)' : '(player)';
            const score = player.promoted_from_viewer ? `${player.viewing_sessions} sessions` : `${player.moves} moves`;
            console.log(`   #${player.rank}: ${player.percentage.toFixed(2)}% - ${player.username} ${type} - ${score}`);
        });

        const totalPlayerSOL = playerRewards.reduce((sum, p) => sum + p.rewardSOL, 0);
        console.log(`   Total distributed: ${totalPlayerSOL.toFixed(9)} SOL`);

        return playerRewards;
    }

    calculateViewerRewards(totalForViewers, viewers) {
        console.log(`\n👥 Calculating viewer rewards...`);

        if (viewers.length === 0) {
            console.log(`   No viewers to reward`);
            return [];
        }

        // Equal distribution among all viewers
        const rewardPerViewer = totalForViewers / viewers.length;

        console.log(`   SOL per viewer: ${rewardPerViewer.toFixed(9)} SOL`);

        const viewerRewards = viewers.map((viewer, index) => ({
            rank: index + 1,
            username: viewer.username,
            user_address: viewer.user_address,
            viewing_sessions: viewer.viewing_sessions || 0,
            rewardSOL: rewardPerViewer,
            percentage: (1 / viewers.length) * 100
        }));

        console.log(`   Calculated equal rewards for ${viewerRewards.length} viewers`);

        return viewerRewards;
    }

    printRewardSummary(distribution) {
        console.log('\n' + '='.repeat(80));
        console.log('🎯 REWARD DISTRIBUTION SUMMARY');
        console.log('='.repeat(80));

        console.log(`\n💰 Total Creator Vault Balance: ${distribution.totalSOL.toFixed(9)} SOL`);
        console.log(`💎 Total Available for Rewards: ${distribution.totalForRewards.toFixed(9)} SOL`);

        // Show promotion info if applicable
        if (distribution.promotedViewers > 0) {
            console.log(`\n🔄 VIEWER PROMOTION:`);
            console.log(`   Original players: ${distribution.originalPlayers}`);
            console.log(`   Promoted viewers: ${distribution.promotedViewers}`);
            console.log(`   Remaining viewers: ${distribution.originalViewers - distribution.promotedViewers}`);
        }

        // Player rewards summary with exponential decay
        const playerPoolPct = distribution.promotedViewers > 0 ?
            (this.playerPct + this.viewersPct) * 100 :
            this.playerPct * 100;

        console.log(`\n🎮 PLAYER REWARDS (${playerPoolPct.toFixed(1)}% = ${distribution.totalForPlayers.toFixed(9)} SOL):`);
        if (distribution.playerRewards.length > 0) {
            console.log(`   Exponential decay ranking (80% decay factor):`);

            // Show top 10 with enhanced display
            distribution.playerRewards.slice(0, 10).forEach(player => {
                const type = player.promoted_from_viewer ? '👁️' : '🎮';
                const score = player.promoted_from_viewer ?
                    `${player.viewing_sessions.toString().padStart(3)} sessions` :
                    `${player.moves.toString().padStart(4)} moves`;
                const rewardDisplay = `${player.rewardSOL.toFixed(9)} SOL (${player.percentage.toFixed(2)}%)`;

                console.log(`   ${player.rank.toString().padStart(2)}. ${type} ${player.username.padEnd(18)} | ${score} | ${rewardDisplay}`);
            });

            if (distribution.playerRewards.length > 10) {
                console.log(`   ... and ${distribution.playerRewards.length - 10} more players`);
            }

            // Show decay comparison
            const topReward = distribution.playerRewards[0];
            const secondReward = distribution.playerRewards[1];
            if (topReward && secondReward) {
                const ratio = (secondReward.rewardSOL / topReward.rewardSOL * 100).toFixed(1);
                console.log(`   Decay example: #2 gets ${ratio}% of #1's reward`);
            }

            const totalPlayerSOL = distribution.playerRewards.reduce((sum, p) => sum + p.rewardSOL, 0);
            console.log(`   Total distributed to players: ${totalPlayerSOL.toFixed(9)} SOL`);
        } else {
            console.log(`   No players to reward`);
        }

        // Viewer rewards summary (only if not promoted)
        if (distribution.viewerRewards.length > 0) {
            console.log(`\n👥 VIEWER REWARDS (${(this.viewersPct * 100).toFixed(1)}% = ${distribution.totalForViewers.toFixed(9)} SOL):`);
            console.log(`   Equal distribution among ${distribution.viewerRewards.length} viewers:`);
            console.log(`   Each viewer receives: ${distribution.viewerRewards[0].rewardSOL.toFixed(9)} SOL`);

            // Show first 5 viewers as example
            console.log(`   Sample viewers:`);
            distribution.viewerRewards.slice(0, 5).forEach(viewer => {
                console.log(`   - ${viewer.username.padEnd(20)} | ${viewer.rewardSOL.toFixed(9)} SOL`);
            });

            if (distribution.viewerRewards.length > 5) {
                console.log(`   ... and ${distribution.viewerRewards.length - 5} more viewers`);
            }

            const totalViewerSOL = distribution.viewerRewards.reduce((sum, v) => sum + v.rewardSOL, 0);
            console.log(`   Total distributed to viewers: ${totalViewerSOL.toFixed(9)} SOL`);
        } else if (distribution.promotedViewers === 0) {
            console.log(`\n👥 VIEWER REWARDS (${(this.viewersPct * 100).toFixed(1)}% = ${distribution.totalForViewers.toFixed(9)} SOL):`);
            console.log(`   No viewers to reward`);
        }

        console.log('\n' + '='.repeat(80));
    }

    async calculateAndPrintRewards(players = [], viewers = []) {
        try {
            console.log('\n🚀 Starting reward calculation process...');

            // 1. Get creator vault balance
            const vaultBalance = await this.getCreatorVaultBalance();

            // 2. Calculate reward distribution
            const distribution = this.calculateRewardDistribution(vaultBalance.sol, players, viewers);

            // 3. Print summary
            this.printRewardSummary(distribution);

            return distribution;

        } catch (error) {
            console.error('💥 Error calculating rewards:', error);
            throw error;
        }
    }
}

module.exports = RewardCalculator;
