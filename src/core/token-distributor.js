require('dotenv').config();
const { Connection, PublicKey, Keypair, VersionedTransaction, TransactionMessage, SystemProgram, Transaction } = require('@solana/web3.js');
const { PumpSdk } = require('@pump-fun/pump-sdk');
const { getAssociatedTokenAddress, createTransferInstruction, TOKEN_PROGRAM_ID } = require('@solana/spl-token');
const bs58 = require('bs58').default;

class TokenDistributor {
    constructor() {
        this.connection = null;
        this.wallet = null;
        this.tokenMint = null;
        this.rpcUrl = process.env.RPC_URL || 'https://api.mainnet-beta.solana.com';
        this.coinAddress = process.env.COIN;
        this.privateKey = process.env.PK;
        this.maxHunterRewardUSD = parseFloat(process.env.MAX_HUNTER_REWARD_VALUE_USD) || 100;
        this.maxViewerRewardUSD = parseFloat(process.env.MAX_VIEWER_REWARD_VALUE_USD) || 50;
        this.slippageBps = 100; // 1% slippage
        this.maxRetries = 3;
        this.maxTxSize = 1232; // Max transaction size in bytes
        this.signatureSize = 64; // Size of signature in bytes
    }

    async init() {
        try {
            console.log('🔗 Initializing Token Distributor...');

            // Initialize connection
            this.connection = new Connection(this.rpcUrl, 'confirmed');
            this.pumpSdk = new PumpSdk(this.connection);

            // Validate environment variables
            if (!this.coinAddress) {
                throw new Error('COIN environment variable not set');
            }
            if (!this.privateKey) {
                throw new Error('PK environment variable not set');
            }

            // Initialize wallet from private key
            const secretKey = bs58.decode(this.privateKey);
            this.wallet = Keypair.fromSecretKey(secretKey);

            // Initialize token mint
            this.tokenMint = new PublicKey(this.coinAddress);

            console.log(`✅ Token Distributor initialized`);
            console.log(`   RPC URL: ${this.rpcUrl}`);
            console.log(`   Wallet: ${this.wallet.publicKey.toString()}`);
            console.log(`   Token Mint: ${this.coinAddress}`);
            console.log(`   Max Hunter Reward: $${this.maxHunterRewardUSD}`);
            console.log(`   Max Viewer Reward: $${this.maxViewerRewardUSD}`);

            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Token Distributor:', error);
            throw error;
        }
    }

    async claimFromVault() {
        try {
            const ix = await this.pumpSdk.collectCoinCreatorFeeInstructions(this.wallet.publicKey)

            // Get recent blockhash and add to tx
            const { blockhash } = await this.connection.getLatestBlockhash();

            const collectFeeTx = new Transaction();

            collectFeeTx.add(...ix);
            collectFeeTx.recentBlockhash = blockhash;
            collectFeeTx.feePayer = this.wallet.publicKey;

            collectFeeTx.sign(this.wallet);

            // convert tx to base64
            const transactionBase64 = Buffer.from(collectFeeTx.serialize()).toString('base64');

            const result = await this.executeTransactionWithRetries(transactionBase64);
            return result;

        } catch (error) {
            console.error('❌ Error claiming from vault:', error);
            throw error;
        }
    }

    async swapSOLToToken(solAmount) {
        try {
            console.log(`🔄 Swapping ${solAmount} SOL to ${this.coinAddress}...`);

            const lamports = Math.floor(solAmount * 1e9); // Convert SOL to lamports

            // Get quote from Jupiter
            const quoteResponse = await this.getJupiterQuote(
                'So11111111111111111111111111111111111111112', // SOL mint
                this.coinAddress,
                lamports
            );

            if (!quoteResponse) {
                throw new Error('Failed to get Jupiter quote');
            }

            console.log(`📊 Quote received: ${quoteResponse.outAmount} tokens for ${solAmount} SOL`);

            // Get swap transaction
            const swapTransaction = await this.getJupiterSwapTransaction(quoteResponse);

            // Deserialize the base64 and sign
            const transactionBuf = Buffer.from(swapTransaction, 'base64');
            const transaction = VersionedTransaction.deserialize(transactionBuf);

            transaction.sign([this.wallet]);
            // Serialize to base64
            const transactionBase64 = Buffer.from(transaction.serialize()).toString('base64');

            const result = await this.executeTransactionWithRetries(transactionBase64);

            console.log(`✅ Swap completed: ${result.txid}`);
            console.log(`🔗 https://solscan.io/tx/${result.txid}`);

            return {
                success: true,
                inputAmount: solAmount,
                outputAmount: parseInt(quoteResponse.outAmount),
                txid: result.txid
            };

        } catch (error) {
            console.error('❌ Error swapping SOL to token:', error);
            throw error;
        }
    }

    async getJupiterQuote(inputMint, outputMint, amount) {
        try {
            const url = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${this.slippageBps}`;

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Jupiter quote API error: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('❌ Error getting Jupiter quote:', error);
            return null;
        }
    }

    async getJupiterSwapTransaction(quoteResponse) {
        try {
            const response = await fetch('https://lite-api.jup.ag/swap/v1/swap', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    quoteResponse,
                    userPublicKey: this.wallet.publicKey.toString(),
                    wrapAndUnwrapSol: true
                })
            });

            if (!response.ok) {
                throw new Error(`Jupiter swap API error: ${response.status}`);
            }

            const { swapTransaction } = await response.json();
            return swapTransaction;
        } catch (error) {
            console.error('❌ Error getting Jupiter swap transaction:', error);
            throw error;
        }
    }

    async getTokenPrice(tokenMint) {
        try {
            const url = `https://lite-api.jup.ag/price/v3?ids=${tokenMint}`;

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Jupiter price API error: ${response.status}`);
            }

            const priceData = await response.json();
            return priceData[tokenMint]?.usdPrice || 0;
        } catch (error) {
            console.error('❌ Error getting token price:', error);
            return 0;
        }
    }

    async executeTransactionWithRetries(transactionBase64) {
        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                console.log(`🔄 Transaction attempt ${attempt}/${this.maxRetries}`);

                // Deserialize transaction
                const transactionBuf = Buffer.from(transactionBase64, 'base64');
                const transaction = VersionedTransaction.deserialize(transactionBuf);

                // Sign transaction
                transaction.sign([this.wallet]);

                // Get latest blockhash
                const latestBlockHash = await this.connection.getLatestBlockhash();

                // Send transaction
                const rawTransaction = transaction.serialize();
                const txid = await this.connection.sendRawTransaction(rawTransaction, {
                    skipPreflight: true,
                    maxRetries: 2
                });

                // Confirm transaction
                await this.connection.confirmTransaction({
                    blockhash: latestBlockHash.blockhash,
                    lastValidBlockHeight: latestBlockHash.lastValidBlockHeight,
                    signature: txid
                });

                return { success: true, txid };

            } catch (error) {
                console.error(`❌ Transaction attempt ${attempt} failed:`, error);

                if (attempt === this.maxRetries) {
                    throw error;
                }

                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
            }
        }
    }

    validateRewardAmounts(recipients, tokenPriceUSD) {
        console.log(`🔍 Validating reward amounts against USD limits...`);
        console.log(`   Token price: $${tokenPriceUSD}`);

        const validatedRecipients = recipients.map(recipient => {
            const { rewardType, tokenAmount } = recipient;
            const usdValue = (tokenAmount / 1e6) * tokenPriceUSD; // Assuming 6 decimals

            const maxUSD = rewardType === 'player' ? this.maxHunterRewardUSD : this.maxViewerRewardUSD;

            if (usdValue > maxUSD) {
                const maxTokenAmount = Math.floor((maxUSD / tokenPriceUSD) * 1e6);
                console.log(`⚠️  Capping ${recipient.username} reward: $${usdValue.toFixed(2)} -> $${maxUSD}`);

                return {
                    ...recipient,
                    tokenAmount: maxTokenAmount,
                    originalTokenAmount: tokenAmount,
                    capped: true,
                    cappedFromUSD: usdValue,
                    cappedToUSD: maxUSD
                };
            }

            return {
                ...recipient,
                capped: false,
                usdValue: usdValue
            };
        });

        const cappedCount = validatedRecipients.filter(r => r.capped).length;
        console.log(`✅ Validation complete: ${cappedCount} rewards capped`);

        return validatedRecipients;
    }

    async createTransferBatches(recipients, checkTokenAccounts = false) {
        console.log(`📦 Creating transfer batches for ${recipients.length} recipients...`);
        console.log(`   Token account validation: ${checkTokenAccounts ? 'ON' : 'OFF'}`);

        const batches = [];
        let currentBatch = [];
        let currentBatchSize = 0;

        // Get our token account address
        const sourceTokenAccount = await getAssociatedTokenAddress(
            this.tokenMint,
            this.wallet.publicKey
        );

        for (const recipient of recipients) {
            try {
                // Validate token account if required
                if (checkTokenAccounts) {
                    const tokenAccountExists = await this.checkTokenAccountExists(recipient.tokenAccountAddress);
                    if (!tokenAccountExists) {
                        console.log(`⚠️  Skipping ${recipient.username}: Token account not found`);
                        batches.push({
                            type: 'failed',
                            recipient: recipient,
                            reason: 'Token account not found',
                            transaction: null
                        });
                        continue;
                    }
                }

                // Create transfer instruction
                const transferInstruction = createTransferInstruction(
                    sourceTokenAccount,
                    new PublicKey(recipient.tokenAccountAddress),
                    this.wallet.publicKey,
                    BigInt(recipient.tokenAmount),
                    [],
                    TOKEN_PROGRAM_ID
                );

                // Estimate instruction size (rough estimate)
                const instructionSize = 64; // Approximate size of transfer instruction

                // Check if adding this instruction would exceed transaction size limit
                if (currentBatchSize + instructionSize + this.signatureSize > this.maxTxSize && currentBatch.length > 0) {
                    // Finalize current batch
                    const transaction = await this.createVersionedTransaction(currentBatch);
                    batches.push({
                        type: 'success',
                        recipients: [...currentBatch],
                        transaction: transaction,
                        estimatedSize: currentBatchSize
                    });

                    // Start new batch
                    currentBatch = [];
                    currentBatchSize = 0;
                }

                // Add to current batch
                currentBatch.push({
                    ...recipient,
                    instruction: transferInstruction
                });
                currentBatchSize += instructionSize;

            } catch (error) {
                console.error(`❌ Error processing ${recipient.username}:`, error);
                batches.push({
                    type: 'failed',
                    recipient: recipient,
                    reason: error.message,
                    transaction: null
                });
            }
        }

        // Finalize last batch if it has instructions
        if (currentBatch.length > 0) {
            try {
                const transaction = await this.createVersionedTransaction(currentBatch);
                batches.push({
                    type: 'success',
                    recipients: [...currentBatch],
                    transaction: transaction,
                    estimatedSize: currentBatchSize
                });
            } catch (error) {
                console.error('❌ Error creating final batch transaction:', error);
                // Mark all recipients in this batch as failed
                currentBatch.forEach(recipient => {
                    batches.push({
                        type: 'failed',
                        recipient: recipient,
                        reason: 'Failed to create transaction',
                        transaction: null
                    });
                });
            }
        }

        const successBatches = batches.filter(b => b.type === 'success').length;
        const failedBatches = batches.filter(b => b.type === 'failed').length;

        console.log(`✅ Batch creation complete:`);
        console.log(`   Success batches: ${successBatches}`);
        console.log(`   Failed recipients: ${failedBatches}`);

        return batches;
    }

    async createVersionedTransaction(recipients) {
        try {
            const instructions = recipients.map(r => r.instruction);

            // Get latest blockhash
            const { blockhash } = await this.connection.getLatestBlockhash();

            // Create transaction message
            const messageV0 = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: instructions
            }).compileToV0Message();

            // Create versioned transaction
            const transaction = new VersionedTransaction(messageV0);

            // Check transaction size
            const serializedSize = transaction.serialize().length + this.signatureSize;
            if (serializedSize > this.maxTxSize) {
                throw new Error(`Transaction too large: ${serializedSize} bytes (max: ${this.maxTxSize})`);
            }

            return transaction;
        } catch (error) {
            console.error('❌ Error creating versioned transaction:', error);
            throw error;
        }
    }

    async checkTokenAccountExists(tokenAccountAddress) {
        try {
            const accountInfo = await this.connection.getAccountInfo(new PublicKey(tokenAccountAddress));
            return accountInfo !== null;
        } catch (error) {
            console.error(`❌ Error checking token account ${tokenAccountAddress}:`, error);
            return false;
        }
    }

    async executeBatches(batches) {
        console.log(`🚀 Executing ${batches.length} batches...`);

        const results = [];

        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];

            if (batch.type === 'failed') {
                results.push(batch);
                continue;
            }

            try {
                console.log(`📤 Executing batch ${i + 1}/${batches.length} (${batch.recipients.length} recipients)`);

                const result = await this.executeTransactionWithRetries(
                    Buffer.from(batch.transaction.serialize()).toString('base64')
                );

                results.push({
                    type: 'success',
                    recipients: batch.recipients,
                    txid: result.txid,
                    estimatedSize: batch.estimatedSize
                });

                console.log(`✅ Batch ${i + 1} completed: ${result.txid}`);
                console.log(`🔗 https://solscan.io/tx/${result.txid}`);

                // Small delay between batches
                if (i < batches.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

            } catch (error) {
                console.error(`❌ Batch ${i + 1} failed:`, error);

                // Mark all recipients in this batch as failed
                batch.recipients.forEach(recipient => {
                    results.push({
                        type: 'failed',
                        recipient: recipient,
                        reason: error.message,
                        transaction: null
                    });
                });
            }
        }

        const successfulTxs = results.filter(r => r.type === 'success').length;
        const failedRecipients = results.filter(r => r.type === 'failed').length;

        console.log(`🎯 Batch execution complete:`);
        console.log(`   Successful transactions: ${successfulTxs}`);
        console.log(`   Failed recipients: ${failedRecipients}`);

        return results;
    }
}

module.exports = TokenDistributor;
